#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add company size vs credit amount validation questions to the permanent question bank.
This script reads the existing question bank Excel files and adds the new validation questions.
"""

import pandas as pd
import json
import os
from datetime import datetime

def load_company_credit_questions():
    """Load the company credit validation questions from JSON file."""
    with open('company_credit_validation_questions.json', 'r') as f:
        data = json.load(f)
    return data['company_credit_validation_questions']

def add_questions_to_excel(excel_file_path, new_questions):
    """Add new questions to an existing Excel file."""
    try:
        # Read existing Excel file
        if os.path.exists(excel_file_path):
            df = pd.read_excel(excel_file_path)
            print(f"📊 Loaded existing question bank: {excel_file_path}")
            print(f"   Current questions: {len(df)}")
        else:
            # Create new DataFrame with expected columns
            df = pd.DataFrame(columns=[
                'ID', 'Question', 'Darwin Reference Sections', 'Expected Outcome', 
                'Client Specific Type', 'Description', 'Category', 'Priority',
                'Added Date', 'Added By'
            ])
            print(f"📊 Creating new question bank: {excel_file_path}")
        
        # Prepare new rows
        new_rows = []
        for question in new_questions:
            new_row = {
                'ID': question['id'],
                'Question': question['question'],
                'Darwin Reference Sections': question['darwin_reference_sections'],
                'Expected Outcome': question['expected_outcome'],
                'Client Specific Type': question['client_specific_type'],
                'Description': question['description'],
                'Category': 'Company Credit Validation',
                'Priority': 'High',
                'Added Date': datetime.now().strftime('%Y-%m-%d'),
                'Added By': 'AI System - Company Credit Enhancement'
            }
            new_rows.append(new_row)
        
        # Add new rows to DataFrame
        new_df = pd.DataFrame(new_rows)
        updated_df = pd.concat([df, new_df], ignore_index=True)
        
        # Save updated Excel file
        updated_df.to_excel(excel_file_path, index=False)
        print(f"✅ Updated {excel_file_path}")
        print(f"   Total questions: {len(updated_df)} (+{len(new_questions)} new)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating {excel_file_path}: {str(e)}")
        return False

def main():
    """Main function to add company credit validation questions to question banks."""
    print("🚀 Adding Company Credit Validation Questions to Question Bank")
    print("=" * 70)
    
    # Load new questions
    try:
        new_questions = load_company_credit_questions()
        print(f"📋 Loaded {len(new_questions)} new validation questions:")
        for i, q in enumerate(new_questions, 1):
            print(f"   {i}. {q['id']}: {q['description']}")
    except Exception as e:
        print(f"❌ Error loading questions: {str(e)}")
        return False
    
    # Find Excel files in data directory
    data_dir = './data'
    excel_files = []
    
    if os.path.exists(data_dir):
        for file in os.listdir(data_dir):
            if file.endswith('.xlsx') and 'Prompts' in file:
                excel_files.append(os.path.join(data_dir, file))
    
    if not excel_files:
        # Create a new question bank file
        excel_files = ['./data/Company_Credit_Validation_Questions.xlsx']
        print("No existing question bank found, creating new file")
    
    print(f"\nFound {len(excel_files)} Excel files to update:")
    for file in excel_files:
        print(f"   - {file}")
    
    # Update each Excel file
    success_count = 0
    for excel_file in excel_files:
        print(f"\n📝 Processing: {excel_file}")
        if add_questions_to_excel(excel_file, new_questions):
            success_count += 1
    
    # Summary
    print(f"\n📊 Summary:")
    print(f"   Files processed: {len(excel_files)}")
    print(f"   Files updated successfully: {success_count}")
    print(f"   Questions added per file: {len(new_questions)}")
    
    if success_count == len(excel_files):
        print("\n🎉 All question banks updated successfully!")
        print("\n📝 Next Steps:")
        print("1. Review the updated Excel files to ensure questions are correctly formatted")
        print("2. Test the validation API with the new questions")
        print("3. Deploy the updated question bank to production")
        return True
    else:
        print(f"\n⚠️  {len(excel_files) - success_count} files failed to update")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
