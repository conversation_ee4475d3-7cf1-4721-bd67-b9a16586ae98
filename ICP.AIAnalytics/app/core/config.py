from pydantic_settings import BaseSettings
from typing import List, Dict, ClassVar
import os
from pathlib import Path

class Settings(BaseSettings):
    # Application Configuration
    APP_NAME: str = "XML Report Validation System"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = ""
    OPENAI_MODEL: str = "gpt-4o-mini"  # Can be changed to gpt-4o, gpt-3.5-turbo, etc.

    # Validation Configuration
    ENABLE_HOLISTIC_VALIDATION: bool = True  # Enable holistic validation approach
    ENABLE_PERFORMANCE_OPTIMIZATIONS: bool = True  # Enable caching and other optimizations
    SKIP_RAG_FOR_SPEED: bool = False # Skip RAG retrieval for faster processing
    REDUCE_LLM_CONTEXT: bool = False   # Reduce XML content sent to LLM
    ENABLE_LLM_BATCHING: bool = False  # Enable LLM batching for multiple questions
    DISABLE_LLM_CACHE: bool = True  # Disable LLM response caching (for testing prompt changes)
    
    # External API Configuration - Import from .env
    EXTERNAL_API_BASE_URL: str = ""  # Will be loaded from .env
    
    # Local data paths
    UPLOAD_PATH: str = "./uploads"
    PROCESSED_PATH: str = "./data/processed"
    
    # Vector Database Configuration 
    CHROMADB_MODE: str = "embedded"  # "embedded" or "server"
    CHROMADB_HOST: str = "localhost"
    CHROMADB_PORT: int = 8001
    CHROMADB_PATH: str = "./data/vector_db"
    VECTOR_DB_PATH: str = "../VectorDB/embedded"  # Parallel directory for vector storage
    
    # LangChain Configuration
    MAX_TOKENS: int = 10000
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    
    # LLM Semantic Search Configuration
    ENABLE_LLM_SEMANTIC_SEARCH: bool = True
    SEMANTIC_SEARCH_CONTENT_LIMIT: int = 12000
    SEMANTIC_SEARCH_MAX_CHUNKS: int = 3
    SEMANTIC_SEARCH_CHUNK_SIZE: int = 8000
    SEMANTIC_SEARCH_MIN_EXTRACTION_SIZE: int = 2000
    
    # Performance Configuration
    DEFAULT_BATCH_SIZE: int = 10
    USE_BULK_PROCESSING: bool = True
    ENABLE_PERFORMANCE_OPTIMIZATIONS: bool = True
    
    # File Configuration
    MAX_FILE_SIZE: int = ********  # 10MB
    ALLOWED_EXCEL_EXTENSIONS: List[str] = [".xlsx", ".xls"]
    ALLOWED_XML_EXTENSIONS: List[str] = [".xml"]
    
    # Permanent Question Bank Configuration
    USE_PERMANENT_QUESTION_BANK: bool = True
    PERMANENT_QUESTION_BANK_PATH: str = ""
    PERMANENT_QUESTION_BANK_SHEET: str = ""
    PERMANENT_QUESTION_BANK_AUTO_RELOAD: bool = False
    
    # Model-specific token limits (for reference only)
    MODEL_TOKEN_LIMITS: ClassVar[Dict[str, int]] = {
        "gpt-4o": 128000,
        "gpt-4o-mini": 128000,
        "gpt-4": 8192,
        "gpt-3.5-turbo": 4096
    }
    
    class Config:
        env_file = ".env"
        case_sensitive = True

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._setup_directories()
    
    def _setup_directories(self):
        """Set up required directories."""
        # Set up local data directories
        Path(self.UPLOAD_PATH).mkdir(parents=True, exist_ok=True)
        Path(self.PROCESSED_PATH).mkdir(parents=True, exist_ok=True)
        
        # Set up vector database directories (for appearance)
        if self.CHROMADB_MODE == "embedded":
            Path(self.CHROMADB_PATH).mkdir(parents=True, exist_ok=True)
        
        # Set up parallel VectorDB directory structure
        vectordb_root = Path(self.VECTOR_DB_PATH).parent
        vectordb_root.mkdir(parents=True, exist_ok=True)
        
        embedded_dir = vectordb_root / "embedded"
        docker_dir = vectordb_root / "docker"
        
        embedded_dir.mkdir(parents=True, exist_ok=True)
        docker_dir.mkdir(parents=True, exist_ok=True)
    
    @property
    def vector_db_absolute_path(self) -> Path:
        """Get absolute path to VectorDB directory."""
        return Path(self.VECTOR_DB_PATH).resolve()
    
    @property
    def is_vector_db_accessible(self) -> bool:
        """Check if VectorDB directory is accessible."""
        try:
            vectordb_path = self.vector_db_absolute_path
            return vectordb_path.exists() and os.access(vectordb_path, os.W_OK)
        except Exception:
            return False
    
    @property
    def is_system_ready(self) -> bool:
        """Check if the system is properly configured."""
        try:
            upload_path = Path(self.UPLOAD_PATH).resolve()
            processed_path = Path(self.PROCESSED_PATH).resolve()
            return upload_path.exists() and processed_path.exists() and os.access(upload_path, os.W_OK) and os.access(processed_path, os.W_OK)
        except Exception:
            return False

# Create global settings instance
settings = Settings() 
